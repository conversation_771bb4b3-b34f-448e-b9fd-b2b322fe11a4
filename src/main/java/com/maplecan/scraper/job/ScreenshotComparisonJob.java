package com.maplecan.scraper.job;

import com.maplecan.scraper.model.ImageComparisonResult;
import com.maplecan.scraper.model.ScreenshotData;
import com.maplecan.scraper.repository.ScreenshotDataRepository;
import com.maplecan.scraper.service.BaselineImageService;
import com.maplecan.scraper.service.EmailService;
import com.maplecan.scraper.service.ImageComparisonService;
import com.maplecan.scraper.service.OpenAIVisionService;
import com.maplecan.scraper.service.ScreenshotService;
import com.maplecan.scraper.service.SupabaseStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScreenshotComparisonJob {

    private final ScreenshotDataRepository screenshotDataRepository;
    private final ScreenshotService screenshotService;
    private final BaselineImageService baselineImageService;
    private final ImageComparisonService imageComparisonService;
    private final SupabaseStorageService supabaseStorageService;
    private final OpenAIVisionService openAIVisionService;
    private final EmailService emailService;

    @Value("${supabase.service.key:}")
    private String supabaseServiceKey;

    /**
     * Daily job to check for changes in tracked website screenshots
     * Runs every day at 2:00 AM UTC
     */
    @Scheduled(cron = "0 0 2 * * *")
//    @Scheduled(cron = "0 * * * * *")
    public void checkScreenshotChanges() {
        log.info("Starting daily screenshot comparison job...");
        
        try {
            // Get all active screenshot tracking records (not deleted and have coordinates)
            List<ScreenshotData> activeScreenshots = screenshotDataRepository.findByDeletedFalseAndCoordinatesIsNotNull();
            
            log.info("Found {} active screenshot tracking records to process", activeScreenshots.size());
            
            for (ScreenshotData screenshot : activeScreenshots) {
                try {
                    processScreenshotComparison(screenshot);
                } catch (Exception e) {
                    log.error("Failed to process screenshot comparison for ID: {}", screenshot.getId(), e);
                    // Continue with next screenshot even if one fails
                }
            }
            
            log.info("Daily screenshot comparison job completed successfully");
        } catch (Exception e) {
            log.error("Error during daily screenshot comparison job", e);
        }
    }

    /**
     * Process individual screenshot comparison
     */
    private void processScreenshotComparison(ScreenshotData screenshotData) {
        log.info("Processing screenshot comparison for ID: {}, URL: {}",
                screenshotData.getId(), screenshotData.getUrl());

        try {
            // Capture new screenshot first (regardless of baseline existence)
            String newScreenshotUrl = null;
            boolean captureSuccessful = false;

            try {
                newScreenshotUrl = captureNewScreenshot(screenshotData);
                captureSuccessful = true;
                log.info("Successfully captured and uploaded screenshot for ID: {}", screenshotData.getId());
            } catch (IOException e) {
                // Check if it's an authentication error (expected in development)
                if (e.getMessage() != null && e.getMessage().contains("service key not configured")) {
                    log.info("Skipping upload for ID: {} - development environment (no service key)", screenshotData.getId());
                } else if (e.getMessage() != null && e.getMessage().contains("403")) {
                    log.info("Skipping upload for ID: {} - authentication error (403)", screenshotData.getId());
                } else {
                    log.error("Failed to capture screenshot for ID: {}", screenshotData.getId(), e);
                }
                captureSuccessful = false;
            }

            // Always update scraped images history (with success URL or FAILED)
            updateScrapedImagesHistory(screenshotData, newScreenshotUrl);

            // Skip comparison if capture failed
            if (!captureSuccessful) {
                log.info("Skipping comparison for ID: {} due to capture/upload failure", screenshotData.getId());
                return;
            }

            // Check if baseline image exists, create one if missing
            String baselineImageUrl = screenshotData.getBaselineImageUrl();
            if (baselineImageUrl == null || baselineImageUrl.isEmpty()) {
                log.info("No baseline image found for screenshot ID: {}, attempting to create one from stored screenshot", screenshotData.getId());

                try {
                    baselineImageUrl = createBaselineFromStoredScreenshot(screenshotData);
                    if (baselineImageUrl != null) {
                        log.info("Successfully created baseline image for screenshot ID: {}", screenshotData.getId());

                        // Update the screenshot data with the new baseline URL
                        screenshotData.setBaselineImageUrl(baselineImageUrl);
                        screenshotDataRepository.save(screenshotData);
                    } else {
                        log.warn("Failed to create baseline image for screenshot ID: {}, skipping comparison", screenshotData.getId());
                        return;
                    }
                } catch (Exception e) {
                    log.error("Error creating baseline image for screenshot ID: {}, skipping comparison", screenshotData.getId(), e);
                    return;
                }
            }
            
            // Extract comparison image from the same area as baseline
            String comparisonImageUrl = baselineImageService.extractAndSaveComparisonImage(
                newScreenshotUrl, 
                screenshotData.getCoordinates(),
                screenshotData.getUserId(),
                screenshotData.getSupabaseId(),
                supabaseServiceKey
            );
            
            // Compare baseline with new cropped image
            ImageComparisonResult comparisonResult = imageComparisonService.compareImages(
                baselineImageUrl,
                comparisonImageUrl
            );

            // Initialize AI analysis variables
            OpenAIVisionService.PriceDropAnalysis aiAnalysis = null;
            boolean aiDetectedPriceDrop = false;

            // If image comparison detects changes, run AI analysis for price drop detection
            if (comparisonResult.isHasDifferences()) {
                log.info("Image differences detected, running AI analysis for price drop detection...");
                try {
                    aiAnalysis = openAIVisionService.analyzePriceDrop(
                        baselineImageUrl,
                        comparisonImageUrl,
                        screenshotData.getUrl()
                    );
                    aiDetectedPriceDrop = aiAnalysis.isHasPriceDrop();

                    log.info("AI analysis completed - Price drop detected: {}, Confidence: {}",
                            aiDetectedPriceDrop, aiAnalysis.getConfidence());
                } catch (Exception e) {
                    log.error("Error during AI analysis for screenshot ID: {}", screenshotData.getId(), e);
                }
            }

            // Create change detection record
            ScreenshotData.ChangeDetection changeDetection = ScreenshotData.ChangeDetection.builder()
                .detectedAt(LocalDateTime.now())
                .newScreenshotUrl(newScreenshotUrl)
                .comparisonImageUrl(comparisonImageUrl)
                .changeDetected(comparisonResult.isHasDifferences())
                .changeDescription(generateChangeDescription(comparisonResult, aiAnalysis))
                .aiPriceDropDetected(aiDetectedPriceDrop)
                .aiConfidence(aiAnalysis != null ? aiAnalysis.getConfidence() : 0.0)
                .aiReasoning(aiAnalysis != null ? aiAnalysis.getReasoning() : null)
                .build();
            
            // Add to change history
            screenshotData.getChangeHistory().add(changeDetection);
            screenshotData.setLastComparisonCheck(LocalDateTime.now());

            // Update baseline image if AI detected a price drop
            if (aiDetectedPriceDrop && aiAnalysis != null) {
                log.info("Price drop detected - updating baseline image for screenshot ID: {}", screenshotData.getId());
                screenshotData.setBaselineImageUrl(comparisonImageUrl);
            }

            // Save updated screenshot data
            screenshotDataRepository.save(screenshotData);

            // Send email notification with 7-day throttling - only when AI detects a price drop with sufficient confidence
            double minConfidenceThreshold = 0.6; // Minimum 60% confidence required
            boolean hasHighConfidence = aiAnalysis != null && aiAnalysis.getConfidence() >= minConfidenceThreshold;

            if (comparisonResult.isHasDifferences() && aiAnalysis != null && aiDetectedPriceDrop && hasHighConfidence && shouldSendEmailNotification(screenshotData)) {
                try {
                    sendPriceDropNotification(screenshotData, aiAnalysis, baselineImageUrl, comparisonImageUrl);

                    // Update lastEmail timestamp after successful email send
                    screenshotData.setLastEmail(LocalDateTime.now());
                    screenshotDataRepository.save(screenshotData);

                    log.info("Price drop notification sent for screenshot ID: {} (validation mode)", screenshotData.getId());
                } catch (Exception e) {
                    log.error("Failed to send price drop notification for screenshot ID: {}", screenshotData.getId(), e);
                }
            } else if (aiAnalysis != null && aiDetectedPriceDrop && !hasHighConfidence) {
                log.info("Skipping email notification for screenshot ID: {} - confidence too low: {:.1f}% (minimum: {:.1f}%)",
                        screenshotData.getId(), aiAnalysis.getConfidence() * 100, minConfidenceThreshold * 100);
            } else if (comparisonResult.isHasDifferences() && aiAnalysis != null && aiDetectedPriceDrop) {
                log.info("Price drop email notification skipped for screenshot ID: {} due to 7-day cooldown", screenshotData.getId());
            } else if (comparisonResult.isHasDifferences() && aiAnalysis != null && !aiDetectedPriceDrop) {
                log.info("Email notification skipped for screenshot ID: {} - AI detected changes but no price drop", screenshotData.getId());
            }

            // Log results
            if (comparisonResult.isHasDifferences()) {
                System.out.println("=== CHANGE DETECTED ===");
                System.out.println("Screenshot ID: " + screenshotData.getId());
                System.out.println("URL: " + screenshotData.getUrl());
                System.out.println("User ID: " + screenshotData.getUserId());
                System.out.println("Overall Similarity: " + String.format("%.2f%%", comparisonResult.getOverallSimilarity() * 100));
                System.out.println("Baseline Image: " + screenshotData.getBaselineImageUrl());
                System.out.println("New Screenshot: " + newScreenshotUrl);
                System.out.println("Comparison Image: " + comparisonImageUrl);
                System.out.println("Change Description: " + changeDetection.getChangeDescription());
                System.out.println("AI Price Drop: " + aiDetectedPriceDrop + " (confidence: " + (aiAnalysis != null ? aiAnalysis.getConfidence() : "N/A") + ")");
                System.out.println("Detected At: " + changeDetection.getDetectedAt());
                System.out.println("========================");

                log.info("Change detected for screenshot ID: {} with similarity: {}",
                        screenshotData.getId(), comparisonResult.getOverallSimilarity());
            } else {
                log.debug("No significant changes detected for screenshot ID: {}", screenshotData.getId());
            }
            
        } catch (Exception e) {
            log.error("Error processing screenshot comparison for ID: {}", screenshotData.getId(), e);
        }
    }

    /**
     * Capture new screenshot for comparison
     */
    private String captureNewScreenshot(ScreenshotData screenshotData) throws IOException {
        try {
            // Use the screenshot service to capture a new screenshot
            byte[] screenshotBytes = screenshotService.captureScreenshotBytes(screenshotData.getUrl());

            if (screenshotBytes == null || screenshotBytes.length == 0) {
                log.error("Screenshot capture returned empty result for URL: {}", screenshotData.getUrl());
                throw new IOException("Screenshot capture returned empty result");
            }

            log.info("Successfully captured screenshot for URL: {} (Size: {} bytes)",
                    screenshotData.getUrl(), screenshotBytes.length);

            // Check if service key is available for upload
            if (supabaseServiceKey == null || supabaseServiceKey.trim().isEmpty()) {
                log.warn("Supabase service key not configured - skipping upload for development environment");
                throw new IOException("Supabase service key not configured for automated uploads");
            }

            // Upload new screenshot to Supabase with organized naming
            return supabaseStorageService.uploadScreenshotWithDataId(
                screenshotBytes,
                screenshotData.getUserId(),
                screenshotData.getSupabaseId(),
                screenshotData.getUrl(),
                supabaseServiceKey
            );

        } catch (Exception e) {
            log.error("Failed to capture/upload new screenshot for URL: {}", screenshotData.getUrl(), e);
            throw new IOException("Failed to capture new screenshot: " + e.getMessage(), e);
        }
    }

    /**
     * Update scraped images history with new screenshot
     */
    private void updateScrapedImagesHistory(ScreenshotData screenshotData, String newScreenshotUrl) {
        try {
            // Create date key for today
            String dateKey = LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // Initialize scrapedImages map if null
            if (screenshotData.getScrapedImages() == null) {
                screenshotData.setScrapedImages(new java.util.HashMap<>());
            }

            // Add new screenshot URL to history (or "FAILED" if capture failed)
            String urlToStore = (newScreenshotUrl != null) ? newScreenshotUrl : "FAILED";
            screenshotData.getScrapedImages().put(dateKey, urlToStore);

            // Update timestamp
            screenshotData.setLastComparisonCheck(LocalDateTime.now());
            screenshotData.setUpdatedAt(LocalDateTime.now());

            // Save updated screenshot data
            screenshotDataRepository.save(screenshotData);

            log.info("Updated scraped images history for screenshot ID: {} with URL: {}",
                    screenshotData.getId(), urlToStore);

        } catch (Exception e) {
            log.error("Failed to update scraped images history for screenshot ID: {}",
                    screenshotData.getId(), e);
        }
    }

    /**
     * Generate human-readable change description based on comparison results and AI analysis
     */
    private String generateChangeDescription(ImageComparisonResult result, OpenAIVisionService.PriceDropAnalysis aiAnalysis) {
        if (!result.isHasDifferences()) {
            return "No significant changes detected";
        }

        StringBuilder description = new StringBuilder();
        double similarity = result.getOverallSimilarity() * 100;

        // Start with basic change detection
        if (result.isSignificantDifferences()) {
            description.append("Significant changes detected (").append(String.format("%.1f", similarity)).append("% similarity)");
        } else {
            description.append("Minor changes detected (").append(String.format("%.1f", similarity)).append("% similarity)");
        }

        // Add AI analysis if available
        if (aiAnalysis != null) {
            if (aiAnalysis.isHasPriceDrop()) {
                description.append(" - AI DETECTED PRICE DROP (confidence: ")
                          .append(String.format("%.1f", aiAnalysis.getConfidence() * 100))
                          .append("%)");
            } else {
                description.append(" - AI analysis: no price drop detected");
            }
        }

        // Add specific metrics if available
        if (result.getPixelSimilarity() < 0.9) {
            description.append(", pixel-level differences");
        }
        if (result.getStructuralSimilarity() < 0.9) {
            description.append(", structural changes");
        }
        if (result.getHistogramSimilarity() < 0.9) {
            description.append(", color distribution changes");
        }

        return description.toString();
    }

    /**
     * Create a baseline image from the stored screenshot URL using coordinates from MongoDB
     * This is used when baseline.jpg is missing but we have a stored screenshot and coordinates
     */
    private String createBaselineFromStoredScreenshot(ScreenshotData screenshotData) throws IOException {
        log.info("Creating baseline image from stored screenshot for ID: {}", screenshotData.getId());

        // Check if we have the necessary data
        if (screenshotData.getScreenshotUrl() == null || screenshotData.getScreenshotUrl().isEmpty()) {
            log.warn("No stored screenshot URL found for ID: {}", screenshotData.getId());
            return null;
        }

        if (screenshotData.getCoordinates() == null) {
            log.warn("No coordinates found for ID: {}", screenshotData.getId());
            return null;
        }

        log.info("Using stored screenshot URL: {}", screenshotData.getScreenshotUrl());
        log.info("Using coordinates: {}", screenshotData.getCoordinates());

        try {
            // Use the BaselineImageService to create and save the baseline image
            // This will crop the stored screenshot using the coordinates and save it as baseline.jpg
            String baselineImageUrl = baselineImageService.extractAndSaveBaselineImage(
                screenshotData,
                supabaseServiceKey
            );

            log.info("Successfully created baseline image: {}", baselineImageUrl);
            return baselineImageUrl;

        } catch (IOException e) {
            log.error("Failed to create baseline image from stored screenshot for ID: {}", screenshotData.getId(), e);
            throw e;
        }
    }

    /**
     * Check if email notification should be sent (7-day cooldown)
     */
    private boolean shouldSendEmailNotification(ScreenshotData screenshotData) {
        if (screenshotData.getLastEmail() == null) {
            // No previous email sent, allow sending
            return true;
        }

        LocalDateTime lastEmailTime = screenshotData.getLastEmail();
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(1);

        // Allow sending if more than 7 days have passed since last email
        boolean shouldSend = lastEmailTime.isBefore(sevenDaysAgo);

        if (!shouldSend) {
            log.info("Email throttled for screenshot ID: {} - last email sent on {}",
                    screenshotData.getId(), lastEmailTime);
        }

        return shouldSend;
    }

    /**
     * Send email notification when AI detects a price drop
     */
    private void sendPriceDropNotification(ScreenshotData screenshotData,
                                         OpenAIVisionService.PriceDropAnalysis aiAnalysis,
                                         String baselineImageUrl,
                                         String comparisonImageUrl) {
        try {
            // Create email content with professional subject line
            String websiteName = extractDomainFromUrl(screenshotData.getUrl());
            String subject = aiAnalysis.isHasPriceDrop() ?
                "Price Alert: " + websiteName + " - Price Drop Detected" :
                "Website Alert: " + websiteName + " - Change Detected";

            String htmlContent = createPriceDropEmailHtml(screenshotData, aiAnalysis, baselineImageUrl, comparisonImageUrl);
            String textContent = createPriceDropEmailText(screenshotData, aiAnalysis);

            // Create email request - <NAME_EMAIL> for validation
            com.maplecan.scraper.model.email.EmailRequest emailRequest =
                com.maplecan.scraper.model.email.EmailRequest.builder()
                    .fromEmail("<EMAIL>")
                    .fromName("BargainHawk Price Monitoring")
                    .toEmail("<EMAIL>") // Hardcoded for validation
                    .toName("Customer")
                    .subject(subject)
                    .htmlContent(htmlContent)
                    .textContent(textContent)
                    .build();

            // Send email
            emailService.sendEmail(emailRequest);

            log.info("Price drop notification email sent to: <EMAIL> (validation mode)");

        } catch (Exception e) {
            log.error("Error sending price drop notification email: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to send price drop notification", e);
        }
    }

    /**
     * Create HTML email content for price drop notification
     */
    private String createPriceDropEmailHtml(ScreenshotData screenshotData,
                                           OpenAIVisionService.PriceDropAnalysis aiAnalysis,
                                           String baselineImageUrl,
                                           String comparisonImageUrl) {
        return String.format("""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Website Change Alert</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
                    .container { max-width: 600px; margin: 0 auto; background-color: white; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #3b82f6 0%%, #1e40af 100%%); color: white; padding: 30px 20px; text-align: center; }
                    .header h1 { margin: 0; font-size: 28px; font-weight: bold; }
                    .header p { margin: 10px 0 0 0; font-size: 16px; opacity: 0.9; }
                    .content { padding: 30px 20px; }
                    .alert-badge { background: #10b981; color: white; padding: 8px 16px; border-radius: 20px; font-size: 12px; font-weight: bold; display: inline-block; margin-bottom: 20px; }
                    .price-section { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3b82f6; }
                    .price-row { display: flex; justify-content: space-between; align-items: center; margin: 10px 0; }
                    .price-label { font-weight: bold; color: #374151; }
                    .price-value { font-size: 18px; font-weight: bold; color: #059669; }
                    .old-price { color: #dc2626; text-decoration: line-through; }
                    .cta-container { text-align: center; margin: 30px 0; }
                    .cta-button {
                        background: linear-gradient(135deg, #3b82f6 0%%, #2563eb 100%%);
                        color: white;
                        padding: 14px 28px;
                        text-decoration: none;
                        border-radius: 8px;
                        font-weight: 600;
                        font-size: 16px;
                        display: inline-block;
                        margin: 0 8px;
                        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
                        transition: all 0.3s ease;
                        border: none;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }
                    .cta-button:hover {
                        background: linear-gradient(135deg, #2563eb 0%%, #1d4ed8 100%%);
                        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
                        transform: translateY(-2px);
                    }
                    .cta-button.secondary {
                        background: linear-gradient(135deg, #6b7280 0%%, #4b5563 100%%);
                        box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
                    }
                    .cta-button.secondary:hover {
                        background: linear-gradient(135deg, #4b5563 0%%, #374151 100%%);
                        box-shadow: 0 6px 16px rgba(107, 114, 128, 0.4);
                    }
                    .screenshot-section { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
                    .screenshot-grid { display: flex; gap: 20px; flex-wrap: wrap; }
                    .screenshot-item { flex: 1; min-width: 250px; text-align: center; }
                    .screenshot-item img { width: 100%%; max-width: 280px; border: 2px solid #e5e7eb; border-radius: 8px; }
                    .footer { background: #f9fafb; padding: 20px; text-align: center; font-size: 14px; color: #6b7280; }
                    .footer a { color: #3b82f6; text-decoration: none; }
                    @media (max-width: 600px) {
                        .price-row { flex-direction: column; align-items: flex-start; }
                        .cta-button { width: 85%%; margin: 8px 0; display: block; }
                        .screenshot-grid { flex-direction: column; }
                        .cta-container { margin: 20px 0; }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Website Change Alert</h1>
                        <p>Your monitored website has been updated</p>
                    </div>

                    <div class="content">
                        <div class="alert-badge">WEBSITE CHANGE DETECTED</div>

                        <div class="price-section">
                            <h3 style="color: #374151; margin-top: 0; margin-bottom: 15px;">AI Analysis Results</h3>
                            <div class="price-row">
                                <span class="price-label">Price Drop Detected:</span>
                                <span class="price-value">%s</span>
                            </div>
                            <div class="price-row">
                                <span class="price-label">Confidence Level:</span>
                                <span class="price-value">%.1f%%</span>
                            </div>
                            <div class="price-row">
                                <span class="price-label">Previous Price:</span>
                                <span class="price-value old-price">%s</span>
                            </div>
                            <div class="price-row">
                                <span class="price-label">Current Price:</span>
                                <span class="price-value">%s</span>
                            </div>
                            <div style="margin-top: 15px; padding: 15px; background: white; border-radius: 6px;">
                                <p style="margin: 0; color: #374151;"><strong>AI Summary:</strong> %s</p>
                            </div>
                        </div>

                        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #6366f1;">
                            <h3 style="color: #374151; margin-top: 0; margin-bottom: 15px;">Product Information</h3>
                            <p style="margin: 8px 0;"><strong>Product:</strong> %s</p>
                            <p style="margin: 8px 0;"><strong>Discount Information:</strong> %s</p>
                            <p style="margin: 8px 0;"><strong>Availability:</strong> %s</p>
                            <p style="margin: 8px 0;"><strong>Promotional Details:</strong> %s</p>
                        </div>

                        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="color: #374151; margin-top: 0; margin-bottom: 15px;">Monitoring Details</h3>
                            <p style="margin: 8px 0;"><strong>Website:</strong> <a href="%s" style="color: #3b82f6; text-decoration: none;">%s</a></p>
                            <p style="margin: 8px 0;"><strong>Monitored Since:</strong> %s</p>
                            <p style="margin: 8px 0;"><strong>Last Check:</strong> %s</p>
                        </div>

                        <div class="screenshot-section">
                            <h3 style="color: #374151; margin-top: 0; margin-bottom: 15px;">Screenshot Comparison</h3>
                            <div class="screenshot-grid">
                                <div class="screenshot-item">
                                    <h4 style="color: #6b7280; margin-bottom: 10px;">Previous Screenshot</h4>
                                    <img src="%s" alt="Previous Screenshot">
                                </div>
                                <div class="screenshot-item">
                                    <h4 style="color: #6b7280; margin-bottom: 10px;">Current Screenshot</h4>
                                    <img src="%s" alt="Current Screenshot">
                                </div>
                            </div>
                        </div>

                        <div class="cta-container">
                            <a href="%s" class="cta-button">View Website</a>
                            <a href="https://bargainhawk.ca/dashboard" class="cta-button secondary">Manage Alerts</a>
                        </div>

                        <div style="background: #f3f4f6; padding: 15px; border-radius: 6px; margin-top: 20px; font-size: 14px; color: #6b7280;">
                            <p style="margin: 0;"><strong>About this alert:</strong> This notification was generated by our AI-powered website monitoring system, which analyzes screenshots to detect price changes and website updates with high accuracy.</p>
                        </div>
                    </div>

                    <div class="footer">
                        <p><strong>BargainHawk Website Monitoring Service</strong></p>
                        <div style="margin: 15px 0;">
                            <a href="https://bargainhawk.ca/dashboard">Manage Alerts</a> |
                            <a href="https://bargainhawk.ca/unsubscribe">Unsubscribe</a>
                        </div>
                        <p style="font-size: 12px; opacity: 0.8; margin-top: 20px;">
                            © 2025 BargainHawk. All rights reserved.<br>
                            This is an automated website monitoring notification for your tracked site.
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """,
            aiAnalysis.isHasPriceDrop() ? "Yes" : "No",
            aiAnalysis.getConfidence() * 100,
            aiAnalysis.getOldPrice() != null ? "$" + aiAnalysis.getOldPrice() : "N/A", // Previous/Old price (higher)
            aiAnalysis.getNewPrice() != null ? "$" + aiAnalysis.getNewPrice() : "N/A", // Current/New price (lower)
            aiAnalysis.getSummary() != null ? aiAnalysis.getSummary() : "Price drop detected in website comparison",
            aiAnalysis.getProductName() != null ? aiAnalysis.getProductName() : "Product details not clearly visible",
            aiAnalysis.getDiscountInfo() != null ? aiAnalysis.getDiscountInfo() : "No discounts visible",
            aiAnalysis.getAvailabilityStatus() != null ? aiAnalysis.getAvailabilityStatus() : "Availability status unclear",
            aiAnalysis.getPromotionalDetails() != null ? aiAnalysis.getPromotionalDetails() : "No special promotions detected",
            screenshotData.getUrl(),
            extractDomainFromUrl(screenshotData.getUrl()),
            screenshotData.getCreatedAt().format(DateTimeFormatter.ofPattern("MMM dd, yyyy")),
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMM dd, yyyy 'at' HH:mm")),
            baselineImageUrl,
            comparisonImageUrl,
            screenshotData.getUrl()
        );
    }

    /**
     * Create plain text email content for price drop notification
     */
    private String createPriceDropEmailText(ScreenshotData screenshotData,
                                          OpenAIVisionService.PriceDropAnalysis aiAnalysis) {
        return String.format("""
            WEBSITE CHANGE ALERT

            Your website monitoring alert has been triggered. Our AI system has detected changes on one of your tracked websites.

            AI Analysis Results:
            - Price Drop Detected: %s
            - Confidence Level: %.1f%%
            - Previous Price: %s
            - Current Price: %s
            - AI Summary: %s

            Product Information:
            - Product: %s
            - Discount Information: %s
            - Availability: %s
            - Promotional Details: %s

            Monitoring Details:
            - Website: %s
            - Monitored Since: %s
            - Last Check: %s

            View Website: %s
            Manage Alerts: https://bargainhawk.ca/dashboard

            Best regards,
            BargainHawk Website Monitoring Service

            Manage your alerts: https://bargainhawk.ca/dashboard
            Unsubscribe: https://bargainhawk.ca/unsubscribe
            """,
            aiAnalysis.isHasPriceDrop() ? "Yes" : "No",
            aiAnalysis.getConfidence() * 100,
            aiAnalysis.getOldPrice() != null ? "$" + aiAnalysis.getOldPrice() : "N/A",
            aiAnalysis.getNewPrice() != null ? "$" + aiAnalysis.getNewPrice() : "N/A",
            aiAnalysis.getSummary() != null ? aiAnalysis.getSummary() : "Price drop detected in website comparison",
            aiAnalysis.getProductName() != null ? aiAnalysis.getProductName() : "Product details not clearly visible",
            aiAnalysis.getDiscountInfo() != null ? aiAnalysis.getDiscountInfo() : "No discounts visible",
            aiAnalysis.getAvailabilityStatus() != null ? aiAnalysis.getAvailabilityStatus() : "Availability status unclear",
            aiAnalysis.getPromotionalDetails() != null ? aiAnalysis.getPromotionalDetails() : "No special promotions detected",
            screenshotData.getUrl(),
            screenshotData.getCreatedAt().format(DateTimeFormatter.ofPattern("MMM dd, yyyy")),
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMM dd, yyyy 'at' HH:mm")),
            screenshotData.getUrl()
        );
    }

    /**
     * Extract domain from URL for display purposes
     */
    private String extractDomainFromUrl(String url) {
        try {
            java.net.URL urlObj = new java.net.URL(url);
            return urlObj.getHost();
        } catch (Exception e) {
            return url;
        }
    }
}
