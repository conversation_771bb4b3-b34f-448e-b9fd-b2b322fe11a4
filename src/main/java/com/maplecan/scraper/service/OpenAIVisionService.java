package com.maplecan.scraper.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.Set;

/**
 * Optimized service for analyzing screenshots using OpenAI GPT-4 Vision API
 * to detect price drops and changes in e-commerce websites.
 *
 * This service makes direct HTTP calls to OpenAI's API endpoints with optimizations:
 * - Uses gpt-4o model (more cost-effective than gpt-4o-mini for vision tasks)
 * - Enables JSON response format for reliable parsing
 * - Sends images via URL references with minimal prompt text
 * - Returns structured responses with calculated confidence levels
 * - Temperature set to 0.1 for deterministic price extraction
 *
 * API Documentation: https://platform.openai.com/docs/guides/images-vision
 */
@Slf4j
@Service
public class OpenAIVisionService {

    @Value("${openai.api.key}")
    private String openAiApiKey;

    @Value("${openai.api.url}")
    private String openAiApiUrl;

    @Value("${GPT_4_VISION_MODEL:gpt-4o}")
    private String configuredModel;

    private static final String GPT_4_VISION_MODEL = "gpt-4o";  // Using gpt-4o instead of mini for better vision performance and lower cost
    private static final int MAX_TOKENS = 150;  // Reduced for structured JSON responses
    private static final Set<String> VALID_MIME_TYPES = Set.of("image/jpeg", "image/png", "image/webp", "image/gif");
    private static final long MAX_IMAGE_PIXELS = 15_000_000L; // 15 megapixels
    private static final int MAX_4K_DIMENSION = 3840; // 4K resolution

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public OpenAIVisionService() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Get validated model with override guard
     */
    private String getValidatedModel() {
        if (configuredModel != null && configuredModel.contains("mini")) {
            log.warn("Model override guard: 'mini' not allowed, forcing swap to gpt-4o");
            return GPT_4_VISION_MODEL;
        }
        return configuredModel != null ? configuredModel : GPT_4_VISION_MODEL;
    }

    /**
     * Analyzes two screenshots to detect price drops using GPT-4 Vision
     *
     * @param baselineImageUrl URL of the baseline/original screenshot
     * @param newImageUrl URL of the new screenshot to compare
     * @param websiteUrl The original website URL for context
     * @return Analysis result containing price drop detection and confidence
     */
    public PriceDropAnalysis analyzePriceDrop(String baselineImageUrl, String newImageUrl, String websiteUrl) {
        if (openAiApiKey == null || openAiApiKey.trim().isEmpty()) {
            log.warn("OpenAI API key not configured - skipping AI analysis");
            return PriceDropAnalysis.builder()
                    .hasPriceDrop(false)
                    .confidence(0.0)
                    .reasoning("OpenAI API key not configured")
                    .build();
        }

        try {
            log.info("Analyzing price drop using GPT-4 Vision for website: {}", websiteUrl);

            // Pre-condition A: Validate MIME type = image/jpeg
            if (!validateMimeType(baselineImageUrl) || !validateMimeType(newImageUrl)) {
                return PriceDropAnalysis.builder()
                        .hasPriceDrop(false)
                        .confidence(0.0)
                        .reasoning("Images must be JPEG format (image/jpeg MIME type)")
                        .build();
            }

            // Apply quality guards
            baselineImageUrl = applyQualityGuard(baselineImageUrl);
            newImageUrl = applyQualityGuard(newImageUrl);

            // Test image accessibility
            boolean baselineAccessible = testImageAccessibility(baselineImageUrl);
            boolean newImageAccessible = testImageAccessibility(newImageUrl);

            if (!baselineAccessible || !newImageAccessible) {
                log.warn("Image accessibility issue - Baseline: {}, New: {}", baselineAccessible, newImageAccessible);
                return PriceDropAnalysis.builder()
                        .hasPriceDrop(false)
                        .confidence(0.0)
                        .reasoning("Images not accessible to OpenAI API - check authentication and CORS settings")
                        .build();
            }

            // Create the request payload
            Map<String, Object> requestPayload = createChatCompletionRequest(baselineImageUrl, newImageUrl, websiteUrl);

            // Log model being used for tracing
            log.info("Using model: {}", requestPayload.get("model"));

            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(openAiApiKey);

            // Create HTTP entity
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestPayload, headers);

            // Make API call
            String apiUrl = openAiApiUrl + "/chat/completions";
            ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.info("OpenAI API response received successfully");

                // Parse the response
                String analysisResult = extractContentFromResponse(responseBody);
                log.info("GPT-4 Vision analysis result: {}", analysisResult);

                PriceDropAnalysis result = parseAnalysisResult(analysisResult);

                // If parsing failed (confidence = 0), retry once with stricter prompt
                if (result.getConfidence() == 0.0) {
                    log.warn("JSON parse failed, retrying with stricter prompt");
                    result = retryWithStricterPrompt(baselineImageUrl, newImageUrl, websiteUrl);
                }

                return result;
            } else {
                log.error("OpenAI API returned status: {}", response.getStatusCode());
                return PriceDropAnalysis.builder()
                        .hasPriceDrop(false)
                        .confidence(0.0)
                        .reasoning("API call failed with status: " + response.getStatusCode())
                        .build();
            }

        } catch (Exception e) {
            log.error("Error analyzing price drop with GPT-4 Vision: {}", e.getMessage(), e);
            return PriceDropAnalysis.builder()
                    .hasPriceDrop(false)
                    .confidence(0.0)
                    .reasoning("Error during AI analysis: " + e.getMessage())
                    .build();
        }
    }

    /**
     * Retry with stricter prompt for JSON schema enforcement
     */
    private PriceDropAnalysis retryWithStricterPrompt(String baselineImageUrl, String newImageUrl, String websiteUrl) {
        try {
            log.info("Retrying with stricter JSON prompt");

            // Create stricter request
            Map<String, Object> requestPayload = createStricterChatCompletionRequest(baselineImageUrl, newImageUrl, websiteUrl);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(openAiApiKey);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestPayload, headers);
            String apiUrl = openAiApiUrl + "/chat/completions";
            ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                String analysisResult = extractContentFromResponse(responseBody);
                log.info("Retry analysis result: {}", analysisResult);

                PriceDropAnalysis result = parseAnalysisResult(analysisResult);
                if (result.getConfidence() == 0.0) {
                    log.warn("Retry also failed, returning confidence 0");
                }
                return result;
            }
        } catch (Exception e) {
            log.error("Retry attempt failed: {}", e.getMessage());
        }

        // Return failed result
        return PriceDropAnalysis.builder()
                .hasPriceDrop(false)
                .confidence(0.0)
                .reasoning("JSON parsing failed after retry")
                .rawResponse("Retry failed")
                .build();
    }

    /**
     * Creates the optimized system prompt for price drop analysis
     */
    private String createSystemPrompt() {
        // Base configurable prompt for general website change detection
        return """
            You are a website monitoring assistant analyzing web pages for changes.
            Compare the two images and identify any significant changes between them.
            Focus on content changes, layout modifications, price updates, or any other notable differences.
            Provide a clear summary of what has changed.
            Return ONLY this JSON:
            {"first_price":number,"second_price":number,"changed":boolean,"summary":"description of changes"}
            changed = true if there are any significant changes detected
            """;
    }

    private String createPriceTrackingPrompt() {
        // Enhanced prompt for comprehensive price analysis and product insights
        return """
            You are an expert e-commerce price-monitoring assistant. Analyze both images carefully and provide comprehensive insights.

            ANALYSIS STEPS:
            1. Identify the product name, brand, and key details from both images
            2. List ALL prices visible in each image (regular price, sale price, discounts)
            3. Compare the lowest/final price between the two images
            4. Analyze any promotional changes (new sales, discount percentages, limited-time offers)
            5. Note any significant visual changes (layout, availability, stock status)

            Output EXACTLY in JSON format: {"first_price":number,"second_price":number,"changed":boolean,"summary":string,"product_name":string,"discount_info":string,"availability_status":string,"promotional_details":string} where:
            - first_price/second_price = the lowest/final price the customer would pay (after all discounts)
            - changed = true only if second_price < first_price (actual price drop)
            - summary = comprehensive analysis of changes including price comparison, promotional updates, and key insights (2-3 sentences)
            - product_name = identified product name and brand (or "Product details not clearly visible")
            - discount_info = current discount percentage or promotional pricing details (or "No discounts visible")
            - availability_status = stock status, availability, or shipping info (or "Availability status unclear")
            - promotional_details = any special offers, limited-time deals, or promotional banners (or "No special promotions detected")

            Focus on providing actionable insights that help users understand the significance of any changes detected.
            """;
    }

    /**
     * Creates stricter chat completion request for retry
     */
    private Map<String, Object> createStricterChatCompletionRequest(String baselineImageUrl, String newImageUrl, String websiteUrl) {
        // System message with stricter instructions
        Map<String, Object> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", createPriceTrackingPrompt() + "\nPlease follow the schema exactly. No extra keys or text.");

        // User message with images and minimal text
        List<Map<String, Object>> userContent = Arrays.asList(
            Map.of(
                "type", "image_url",
                "image_url", Map.of("url", baselineImageUrl)
            ),
            Map.of(
                "type", "image_url",
                "image_url", Map.of("url", newImageUrl)
            ),
            Map.of(
                "type", "text",
                "text", "Return ONLY: {\"first_price\":number,\"second_price\":number,\"changed\":boolean}"
            )
        );

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", userContent);

        // Build clean request
        Map<String, Object> request = new HashMap<>();
        request.put("model", getValidatedModel());
        request.put("temperature", 0.1);
        request.put("messages", Arrays.asList(systemMessage, userMessage));
        request.put("response_format", Map.of("type", "json_object"));

        return request;
    }

    /**
     * Creates the optimized chat completion request payload for OpenAI API
     */
    private Map<String, Object> createChatCompletionRequest(String baselineImageUrl, String newImageUrl, String websiteUrl) {
        // System message with concise instructions
        Map<String, Object> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", createPriceTrackingPrompt()); // Using specific price tracking prompt for SSENSE

        // User message with images and clean text (no URL or product mentions)
        List<Map<String, Object>> userContent = Arrays.asList(
            Map.of(
                "type", "image_url",
                "image_url", Map.of("url", baselineImageUrl)
            ),
            Map.of(
                "type", "image_url",
                "image_url", Map.of("url", newImageUrl)
            ),
            Map.of(
                "type", "text",
                "text", "Use ONLY what is visible in the images above. Respond in JSON format."
            )
        );

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", userContent);

        // Clean request payload following exact specifications
        Map<String, Object> request = new HashMap<>();
        request.put("model", getValidatedModel());
        request.put("temperature", 0.1);
        request.put("messages", Arrays.asList(systemMessage, userMessage));

        // Enable JSON mode for structured output
        Map<String, Object> responseFormat = new HashMap<>();
        responseFormat.put("type", "json_object");
        request.put("response_format", responseFormat);

        return request;
    }

    /**
     * Pre-condition check: Validate MIME type is image/jpeg
     */
    private boolean validateMimeType(String imageUrl) {
        try {
            HttpHeaders headers = new HttpHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(imageUrl, HttpMethod.HEAD, entity, String.class);

            String contentType = response.getHeaders().getFirst("Content-Type");
            boolean isValidMime = "image/jpeg".equals(contentType);

            if (!isValidMime) {
                log.warn("MIME type validation failed for {}: expected image/jpeg, got {}", imageUrl, contentType);
            }

            return isValidMime;
        } catch (Exception e) {
            log.warn("MIME type check failed for {}: {}", imageUrl, e.getMessage());
            return false;
        }
    }

    /**
     * Quality guard: Check if image needs downscaling and return appropriate URL
     */
    private String applyQualityGuard(String imageUrl) {
        try {
            // For now, we'll just log the quality check
            // In a full implementation, we would download, check dimensions, and re-upload if needed
            log.info("Quality guard check for image: {}", imageUrl);
            return imageUrl; // Return original URL for now
        } catch (Exception e) {
            log.warn("Quality guard check failed for {}: {}", imageUrl, e.getMessage());
            return imageUrl; // Return original URL on error
        }
    }

    /**
     * Extracts the content from OpenAI API response
     */
    private String extractContentFromResponse(String responseBody) throws Exception {
        JsonNode rootNode = objectMapper.readTree(responseBody);

        // Check for API errors first
        JsonNode errorNode = rootNode.get("error");
        if (errorNode != null) {
            String errorMessage = errorNode.get("message").asText();
            String errorType = errorNode.has("type") ? errorNode.get("type").asText() : "unknown";
            throw new Exception("OpenAI API Error [" + errorType + "]: " + errorMessage);
        }

        JsonNode choicesNode = rootNode.get("choices");

        if (choicesNode != null && choicesNode.isArray() && choicesNode.size() > 0) {
            JsonNode firstChoice = choicesNode.get(0);
            JsonNode messageNode = firstChoice.get("message");
            JsonNode contentNode = messageNode.get("content");

            if (contentNode != null) {
                return contentNode.asText();
            }
        }

        // Log the full response for debugging
        log.warn("Unexpected OpenAI response format: {}", responseBody);
        throw new Exception("Unable to extract content from OpenAI response");
    }

    /**
     * Test if an image URL is accessible
     */
    public boolean testImageAccessibility(String imageUrl) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "BargainHawk-Bot/1.0");

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<byte[]> response = restTemplate.exchange(
                imageUrl,
                HttpMethod.GET,
                entity,
                byte[].class
            );

            boolean accessible = response.getStatusCode().is2xxSuccessful();
            log.info("Image accessibility test for {}: {}", imageUrl, accessible ? "ACCESSIBLE" : "NOT ACCESSIBLE");
            return accessible;

        } catch (Exception e) {
            log.warn("Image accessibility test failed for {}: {}", imageUrl, e.getMessage());
            return false;
        }
    }

    /**
     * Check if image URL has valid MIME type
     */
    public boolean checkMimeType(String imageUrl) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "BargainHawk-Bot/1.0");

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<byte[]> response = restTemplate.exchange(
                imageUrl,
                HttpMethod.GET,
                entity,
                byte[].class
            );

            String contentType = response.getHeaders().getContentType() != null
                ? response.getHeaders().getContentType().toString()
                : "";

            boolean validMime = VALID_MIME_TYPES.stream().anyMatch(contentType::contains);
            if (!validMime) {
                log.info("MIME fix: Invalid content type '{}' for URL: {}", contentType, imageUrl);
            }
            return validMime;

        } catch (Exception e) {
            log.warn("MIME type check failed for {}: {}", imageUrl, e.getMessage());
            return false;
        }
    }

    /**
     * Parses the optimized GPT-4 Vision analysis result into a structured response
     */
    private PriceDropAnalysis parseAnalysisResult(String analysisResult) {
        try {
            // With JSON mode enabled, response should be clean JSON
            JsonNode jsonNode = objectMapper.readTree(analysisResult.trim());

            // Map the comprehensive schema to our structure
            boolean hasPriceDrop = jsonNode.has("changed") ? jsonNode.get("changed").asBoolean() : false;

            // Calculate confidence based on whether prices were detected
            double confidence = 0.0;
            String firstPrice = jsonNode.has("first_price") && !jsonNode.get("first_price").isNull() ?
                               jsonNode.get("first_price").asText() : null;
            String secondPrice = jsonNode.has("second_price") && !jsonNode.get("second_price").isNull() ?
                                jsonNode.get("second_price").asText() : null;

            // Extract comprehensive analysis fields
            String summary = jsonNode.has("summary") ? jsonNode.get("summary").asText() : "Price comparison analysis";
            String productName = jsonNode.has("product_name") ? jsonNode.get("product_name").asText() : "Product details not clearly visible";
            String discountInfo = jsonNode.has("discount_info") ? jsonNode.get("discount_info").asText() : "No discounts visible";
            String availabilityStatus = jsonNode.has("availability_status") ? jsonNode.get("availability_status").asText() : "Availability status unclear";
            String promotionalDetails = jsonNode.has("promotional_details") ? jsonNode.get("promotional_details").asText() : "No special promotions detected";

            // Numeric sanity check and confidence calculation
            if (firstPrice != null && !isValidPrice(firstPrice)) {
                log.warn("Price sanity check failed for first_price: {} - setting confidence to 0", firstPrice);
                confidence = 0.0;
            } else if (secondPrice != null && !isValidPrice(secondPrice)) {
                log.warn("Price sanity check failed for second_price: {} - setting confidence to 0", secondPrice);
                confidence = 0.0;
            } else if (firstPrice != null && secondPrice != null) {
                // Calculate actual price difference for better confidence assessment
                try {
                    double price1 = Double.parseDouble(firstPrice);
                    double price2 = Double.parseDouble(secondPrice);
                    double priceDifference = Math.abs(price1 - price2);
                    double percentageChange = (priceDifference / price1) * 100;

                    if (hasPriceDrop) {
                        // Only high confidence if price drop is significant (>1% or >$2)
                        if (percentageChange >= 1.0 || priceDifference >= 2.0) {
                            confidence = 0.9;
                        } else if (percentageChange >= 0.5 || priceDifference >= 1.0) {
                            confidence = 0.7; // Medium confidence for smaller drops
                        } else {
                            confidence = 0.4; // Low confidence for very small changes
                            log.info("Small price change detected: ${} difference ({}%), reducing confidence",
                                   priceDifference, String.format("%.2f", percentageChange));
                        }
                    } else {
                        confidence = 0.8; // High confidence when both prices detected but no drop
                    }
                } catch (NumberFormatException e) {
                    log.warn("Failed to parse prices for confidence calculation: {} -> {}", firstPrice, secondPrice);
                    confidence = hasPriceDrop ? 0.6 : 0.5; // Fallback confidence
                }
            } else if (firstPrice != null || secondPrice != null) {
                confidence = 0.5; // Medium confidence when only one price detected
            }

            return PriceDropAnalysis.builder()
                    .hasPriceDrop(hasPriceDrop)
                    .confidence(confidence)
                    .oldPrice(firstPrice)
                    .newPrice(secondPrice)
                    .reasoning("Comprehensive price analysis completed")
                    .rawResponse(analysisResult)
                    .summary(summary)
                    .productName(productName)
                    .discountInfo(discountInfo)
                    .availabilityStatus(availabilityStatus)
                    .promotionalDetails(promotionalDetails)
                    .build();

        } catch (Exception e) {
            log.warn("Failed to parse AI analysis result: {}", e.getMessage());
            log.debug("Raw analysis result: {}", analysisResult);

            // Retry with fallback parsing for non-JSON responses
            try {
                String jsonContent = extractJsonFromMarkdown(analysisResult);
                if (jsonContent != null) {
                    return parseAnalysisResult(jsonContent);
                }
            } catch (Exception retryException) {
                log.debug("Fallback parsing also failed: {}", retryException.getMessage());
            }

            // Final fallback: return default analysis
            return PriceDropAnalysis.builder()
                    .hasPriceDrop(false)
                    .confidence(0.0)
                    .reasoning("Failed to parse AI response - please check response format")
                    .rawResponse(analysisResult)
                    .build();
        }
    }

    /**
     * Extracts JSON content from markdown code blocks
     */
    private String extractJsonFromMarkdown(String content) {
        // Look for ```json ... ``` blocks
        String jsonPattern = "```json\\s*\\n([\\s\\S]*?)\\n```";
        Pattern pattern = Pattern.compile(jsonPattern, Pattern.MULTILINE);
        Matcher matcher = pattern.matcher(content);

        if (matcher.find()) {
            return matcher.group(1).trim();
        }

        // Also try generic ``` blocks
        String genericPattern = "```\\s*\\n([\\s\\S]*?)\\n```";
        pattern = Pattern.compile(genericPattern, Pattern.MULTILINE);
        matcher = pattern.matcher(content);

        if (matcher.find()) {
            String extracted = matcher.group(1).trim();
            // Check if it looks like JSON
            if (extracted.startsWith("{") && extracted.endsWith("}")) {
                return extracted;
            }
        }

        return null;
    }

    /**
     * Validates that a price is within reasonable bounds
     */
    private boolean isValidPrice(String priceStr) {
        try {
            double price = Double.parseDouble(priceStr);
            return price >= 1.0 && price <= 10000.0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Data class for price drop analysis results
     */
    public static class PriceDropAnalysis {
        private boolean hasPriceDrop;
        private double confidence;
        private String oldPrice;
        private String newPrice;
        private String reasoning;
        private String rawResponse;
        private String summary;
        private String productName;
        private String discountInfo;
        private String availabilityStatus;
        private String promotionalDetails;

        public static PriceDropAnalysisBuilder builder() {
            return new PriceDropAnalysisBuilder();
        }

        // Getters
        public boolean isHasPriceDrop() { return hasPriceDrop; }
        public double getConfidence() { return confidence; }
        public String getOldPrice() { return oldPrice; }
        public String getNewPrice() { return newPrice; }
        public String getReasoning() { return reasoning; }
        public String getRawResponse() { return rawResponse; }
        public String getSummary() { return summary; }
        public String getProductName() { return productName; }
        public String getDiscountInfo() { return discountInfo; }
        public String getAvailabilityStatus() { return availabilityStatus; }
        public String getPromotionalDetails() { return promotionalDetails; }

        // Builder class
        public static class PriceDropAnalysisBuilder {
            private boolean hasPriceDrop;
            private double confidence;
            private String oldPrice;
            private String newPrice;
            private String reasoning;
            private String rawResponse;
            private String summary;
            private String productName;
            private String discountInfo;
            private String availabilityStatus;
            private String promotionalDetails;

            public PriceDropAnalysisBuilder hasPriceDrop(boolean hasPriceDrop) {
                this.hasPriceDrop = hasPriceDrop;
                return this;
            }

            public PriceDropAnalysisBuilder confidence(double confidence) {
                this.confidence = confidence;
                return this;
            }

            public PriceDropAnalysisBuilder oldPrice(String oldPrice) {
                this.oldPrice = oldPrice;
                return this;
            }

            public PriceDropAnalysisBuilder newPrice(String newPrice) {
                this.newPrice = newPrice;
                return this;
            }

            public PriceDropAnalysisBuilder reasoning(String reasoning) {
                this.reasoning = reasoning;
                return this;
            }

            public PriceDropAnalysisBuilder rawResponse(String rawResponse) {
                this.rawResponse = rawResponse;
                return this;
            }

            public PriceDropAnalysisBuilder summary(String summary) {
                this.summary = summary;
                return this;
            }

            public PriceDropAnalysisBuilder productName(String productName) {
                this.productName = productName;
                return this;
            }

            public PriceDropAnalysisBuilder discountInfo(String discountInfo) {
                this.discountInfo = discountInfo;
                return this;
            }

            public PriceDropAnalysisBuilder availabilityStatus(String availabilityStatus) {
                this.availabilityStatus = availabilityStatus;
                return this;
            }

            public PriceDropAnalysisBuilder promotionalDetails(String promotionalDetails) {
                this.promotionalDetails = promotionalDetails;
                return this;
            }

            public PriceDropAnalysis build() {
                PriceDropAnalysis analysis = new PriceDropAnalysis();
                analysis.hasPriceDrop = this.hasPriceDrop;
                analysis.confidence = this.confidence;
                analysis.oldPrice = this.oldPrice;
                analysis.newPrice = this.newPrice;
                analysis.reasoning = this.reasoning;
                analysis.rawResponse = this.rawResponse;
                analysis.summary = this.summary;
                analysis.productName = this.productName;
                analysis.discountInfo = this.discountInfo;
                analysis.availabilityStatus = this.availabilityStatus;
                analysis.promotionalDetails = this.promotionalDetails;
                return analysis;
            }
        }
    }

    /**
     * Debug method to see what the AI actually sees in the images
     */
    public String debugVisionAnalysis(String baselineImageUrl, String newImageUrl) throws Exception {
        log.info("Starting debug vision analysis");

        // Validate images are accessible
        if (!testImageAccessibility(baselineImageUrl)) {
            throw new RuntimeException("Baseline image not accessible");
        }

        if (!testImageAccessibility(newImageUrl)) {
            throw new RuntimeException("New image not accessible");
        }

        // Create debug request
        Map<String, Object> debugRequest = createDebugChatCompletionRequest(baselineImageUrl, newImageUrl);

        // Call OpenAI API
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(openAiApiKey);

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(debugRequest, headers);

        try {
            String apiUrl = openAiApiUrl + "/chat/completions";
            ResponseEntity<String> response = restTemplate.postForEntity(
                apiUrl,
                entity,
                String.class
            );

            log.info("OpenAI debug API response received successfully");
            return extractContentFromResponse(response.getBody());

        } catch (Exception e) {
            log.error("OpenAI debug API call failed: {}", e.getMessage(), e);
            throw new Exception("Failed to get debug analysis from OpenAI: " + e.getMessage());
        }
    }

    /**
     * Creates debug chat completion request to understand what AI sees
     */
    private Map<String, Object> createDebugChatCompletionRequest(String baselineImageUrl, String newImageUrl) {
        // System message asking AI to describe what it sees
        Map<String, Object> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", "You are analyzing e-commerce product pages. Describe in detail what you see in each image, focusing on all visible prices, their locations, and any price-related elements. Be very specific about the numbers you can see.");

        // User message with images
        List<Map<String, Object>> userContent = Arrays.asList(
            Map.of(
                "type", "image_url",
                "image_url", Map.of("url", baselineImageUrl)
            ),
            Map.of(
                "type", "image_url",
                "image_url", Map.of("url", newImageUrl)
            ),
            Map.of(
                "type", "text",
                "text", "Describe what you see in each image. List all prices and numbers visible. Be very specific about what prices you can identify and where they are located on the page."
            )
        );

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", userContent);

        // Request configuration
        Map<String, Object> request = new HashMap<>();
        request.put("model", "gpt-4o");
        request.put("messages", Arrays.asList(systemMessage, userMessage));
        request.put("max_tokens", 1000);
        request.put("temperature", 0.1);

        return request;
    }
}
